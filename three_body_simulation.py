import numpy as np
from typing import List
import matplotlib.pyplot as plt

class Body:
    """表示一个天体的类"""
    
    def __init__(self, mass: float, position: np.ndarray, velocity: np.ndarray):
        self.mass = mass
        self.position = position.copy()
        self.velocity = velocity.copy()
        self.trajectory = [position.copy()]
        self.force = np.zeros(2)
    
    def update_position(self, dt: float):
        """根据当前速度更新位置"""
        self.velocity += self.force / self.mass * dt
        self.position += self.velocity * dt
        self.trajectory.append(self.position.copy())
    
    def reset_force(self):
        """重置受力"""
        self.force = np.zeros(2)

class ThreeBodySimulation:
    """三体问题模拟类"""
    
    def __init__(self, bodies: List[Body], G: float = 1.0, dt: float = 0.001):
        self.bodies = bodies
        self.G = G  # 引力常数
        self.dt = dt  # 时间步长
        self.time = 0.0
    
    def calculate_forces(self):
        """计算所有天体之间的引力"""
        # 重置所有天体的受力
        for body in self.bodies:
            body.reset_force()
        
        # 计算两两之间的引力
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                
                # 计算距离向量
                r_vec = body2.position - body1.position
                r_magnitude = np.linalg.norm(r_vec)
                
                # 避免奇点
                if r_magnitude < 1e-10:
                    continue
                
                # 计算引力大小
                force_magnitude = self.G * body1.mass * body2.mass / (r_magnitude ** 2)
                
                # 计算引力方向（单位向量）
                force_direction = r_vec / r_magnitude
                
                # 应用牛顿第三定律
                force = force_magnitude * force_direction
                body1.force += force
                body2.force -= force
    
    def update_step(self):
        """执行一个时间步的模拟"""
        self.calculate_forces()
        
        for body in self.bodies:
            body.update_position(self.dt)
        
        self.time += self.dt
    
    def simulate(self, total_time: float, save_interval: int = 100):
        """运行完整的模拟"""
        steps = int(total_time / self.dt)
        
        for step in range(steps):
            self.update_step()
            
            # 定期保存轨迹点以节省内存
            if step % save_interval == 0:
                for body in self.bodies:
                    if len(body.trajectory) > 10000:  # 限制轨迹长度
                        body.trajectory = body.trajectory[-5000:]
    
    def get_total_energy(self):
        """计算系统总能量（动能+势能）"""
        kinetic_energy = 0
        potential_energy = 0
        
        # 计算动能
        for body in self.bodies:
            kinetic_energy += 0.5 * body.mass * np.linalg.norm(body.velocity) ** 2
        
        # 计算势能
        for i in range(len(self.bodies)):
            for j in range(i + 1, len(self.bodies)):
                body1, body2 = self.bodies[i], self.bodies[j]
                r = np.linalg.norm(body2.position - body1.position)
                if r > 1e-10:
                    potential_energy -= self.G * body1.mass * body2.mass / r
        
        return kinetic_energy + potential_energy
    
    def plot_trajectories(self, title="三体运动轨迹"):
        """绘制轨迹图"""
        plt.figure(figsize=(12, 10))
        
        colors = ['red', 'blue', 'green']
        labels = ['质点1', '质点2', '质点3']
        
        for i, body in enumerate(self.bodies):
            trajectory = np.array(body.trajectory)
            plt.plot(trajectory[:, 0], trajectory[:, 1], 
                    color=colors[i], label=f'{labels[i]} (m={body.mass})', 
                    linewidth=1.5, alpha=0.7)
            
            # 标记起始位置
            plt.scatter(trajectory[0, 0], trajectory[0, 1], 
                       color=colors[i], s=100, marker='o', 
                       edgecolors='black', linewidth=2)
            
            # 标记当前位置
            plt.scatter(body.position[0], body.position[1], 
                       color=colors[i], s=200, marker='*', 
                       edgecolors='black', linewidth=2)
        
        plt.xlabel('X 坐标')
        plt.ylabel('Y 坐标')
        plt.title(title)
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.axis('equal')
        plt.tight_layout()
        plt.show()

def create_stable_system():
    """创建一个相对稳定的三体系统"""
    bodies = [
        Body(mass=100, position=np.array([-1.0, 0.0]), velocity=np.array([0.0, -0.2])),
        Body(mass=100, position=np.array([1.0, 0.0]), velocity=np.array([0.0, 0.2])),
        Body(mass=1, position=np.array([0.0, 0.0]), velocity=np.array([0.5, 0.0]))
    ]
    return ThreeBodySimulation(bodies, G=1.0)

def create_chaotic_system():
    """创建一个混沌的三体系统"""
    bodies = [
        Body(mass=150, position=np.array([-2.0, 0.0]), velocity=np.array([0.1, -0.4])),
        Body(mass=30, position=np.array([2.0, 0.0]), velocity=np.array([-0.2, 0.8])),
        Body(mass=5, position=np.array([0.0, 3.0]), velocity=np.array([0.6, 0.1]))
    ]
    return ThreeBodySimulation(bodies, G=0.8)

def create_figure8_system():
    """创建经典的8字形周期轨道"""
    bodies = [
        Body(mass=50, position=np.array([-0.97000436, 0.24208753]), 
             velocity=np.array([0.4662036850, 0.4323657300])),
        Body(mass=50, position=np.array([0.97000436, -0.24208753]), 
             velocity=np.array([0.4662036850, 0.4323657300])),
        Body(mass=50, position=np.array([0.0, 0.0]), 
             velocity=np.array([-0.93240737, -0.86473146]))
    ]
    return ThreeBodySimulation(bodies, G=1.0)

if __name__ == "__main__":
    # 示例：创建并运行一个稳定的三体系统
    print("创建稳定三体系统...")
    simulation = create_stable_system()
    
    print("运行模拟...")
    simulation.simulate(total_time=10.0)
    
    print(f"模拟完成，总时间: {simulation.time:.2f}")
    print(f"系统总能量: {simulation.get_total_energy():.6f}")
    
    # 绘制轨迹
    simulation.plot_trajectories("稳定三体系统轨迹")
