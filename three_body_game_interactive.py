import pygame
import pygame_gui
import numpy as np
from three_body_simulation import ThreeBodySimulation, Body

class InteractiveThreeBodyGame:
    """带有可调节初始条件界面的三体游戏"""
    
    def __init__(self, width=1400, height=900):
        pygame.init()
        self.width = width
        self.height = height
        self.screen = pygame.display.set_mode((width, height))
        pygame.display.set_caption("三体运动轨迹游戏 - 交互式参数调节")
        self.clock = pygame.time.Clock()
        
        # 界面布局
        self.sim_width = 1000  # 模拟区域宽度
        self.panel_width = width - self.sim_width  # 控制面板宽度
        
        # GUI管理器
        self.ui_manager = pygame_gui.UIManager((width, height))
        
        # 游戏状态
        self.running = True
        self.paused = True  # 默认暂停，等待用户设置参数
        self.simulation = None
        self.scale = 60
        self.trail_length = 800
        
        # 初始参数
        self.body_params = [
            {'mass': 100.0, 'pos_x': -2.0, 'pos_y': 0.0, 'vel_x': 0.0, 'vel_y': -0.3},
            {'mass': 50.0, 'pos_x': 2.0, 'pos_y': 0.0, 'vel_x': 0.0, 'vel_y': 0.6},
            {'mass': 10.0, 'pos_x': 0.0, 'pos_y': 2.0, 'vel_x': 0.4, 'vel_y': 0.0}
        ]
        self.gravity_constant = 0.5
        
        self.create_ui()
        self.reset_simulation()
    
    def create_ui(self):
        """创建用户界面"""
        panel_rect = pygame.Rect(self.sim_width, 0, self.panel_width, self.height)
        
        # 标题
        title_rect = pygame.Rect(self.sim_width + 10, 10, self.panel_width - 20, 40)
        self.title_label = pygame_gui.elements.UILabel(
            relative_rect=title_rect,
            text='三体系统参数设置',
            manager=self.ui_manager
        )
        
        y_offset = 60
        
        # 引力常数设置
        gravity_label_rect = pygame.Rect(self.sim_width + 10, y_offset, 150, 25)
        self.gravity_label = pygame_gui.elements.UILabel(
            relative_rect=gravity_label_rect,
            text='引力常数 G:',
            manager=self.ui_manager
        )
        
        gravity_slider_rect = pygame.Rect(self.sim_width + 10, y_offset + 30, 200, 20)
        self.gravity_slider = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=gravity_slider_rect,
            start_value=self.gravity_constant,
            value_range=(0.1, 2.0),
            manager=self.ui_manager
        )
        
        gravity_value_rect = pygame.Rect(self.sim_width + 220, y_offset + 30, 80, 25)
        self.gravity_value_label = pygame_gui.elements.UILabel(
            relative_rect=gravity_value_rect,
            text=f'{self.gravity_constant:.2f}',
            manager=self.ui_manager
        )
        
        y_offset += 80
        
        # 为每个质点创建参数控制
        self.body_controls = []
        colors = ['红色质点', '蓝色质点', '绿色质点']
        
        for i, color_name in enumerate(colors):
            body_controls = self.create_body_controls(i, color_name, y_offset)
            self.body_controls.append(body_controls)
            y_offset += 200
        
        # 控制按钮
        button_y = y_offset + 20
        
        start_button_rect = pygame.Rect(self.sim_width + 10, button_y, 80, 35)
        self.start_button = pygame_gui.elements.UIButton(
            relative_rect=start_button_rect,
            text='开始',
            manager=self.ui_manager
        )
        
        pause_button_rect = pygame.Rect(self.sim_width + 100, button_y, 80, 35)
        self.pause_button = pygame_gui.elements.UIButton(
            relative_rect=pause_button_rect,
            text='暂停',
            manager=self.ui_manager
        )
        
        reset_button_rect = pygame.Rect(self.sim_width + 190, button_y, 80, 35)
        self.reset_button = pygame_gui.elements.UIButton(
            relative_rect=reset_button_rect,
            text='重置',
            manager=self.ui_manager
        )
        
        # 预设配置按钮
        preset_y = button_y + 50
        
        stable_button_rect = pygame.Rect(self.sim_width + 10, preset_y, 120, 30)
        self.stable_preset_button = pygame_gui.elements.UIButton(
            relative_rect=stable_button_rect,
            text='稳定轨道',
            manager=self.ui_manager
        )
        
        chaotic_button_rect = pygame.Rect(self.sim_width + 140, preset_y, 120, 30)
        self.chaotic_preset_button = pygame_gui.elements.UIButton(
            relative_rect=chaotic_button_rect,
            text='混沌轨道',
            manager=self.ui_manager
        )
        
        figure8_button_rect = pygame.Rect(self.sim_width + 10, preset_y + 40, 120, 30)
        self.figure8_preset_button = pygame_gui.elements.UIButton(
            relative_rect=figure8_button_rect,
            text='8字轨道',
            manager=self.ui_manager
        )
    
    def create_body_controls(self, body_index, name, y_start):
        """为单个质点创建控制组件"""
        controls = {}
        
        # 质点名称标签
        name_rect = pygame.Rect(self.sim_width + 10, y_start, 200, 25)
        controls['name_label'] = pygame_gui.elements.UILabel(
            relative_rect=name_rect,
            text=f'{name} (质量: {self.body_params[body_index]["mass"]})',
            manager=self.ui_manager
        )
        
        # 质量滑块
        mass_rect = pygame.Rect(self.sim_width + 10, y_start + 30, 150, 20)
        controls['mass_slider'] = pygame_gui.elements.UIHorizontalSlider(
            relative_rect=mass_rect,
            start_value=self.body_params[body_index]['mass'],
            value_range=(1.0, 200.0),
            manager=self.ui_manager
        )
        
        # 位置控制
        pos_label_rect = pygame.Rect(self.sim_width + 10, y_start + 60, 100, 20)
        controls['pos_label'] = pygame_gui.elements.UILabel(
            relative_rect=pos_label_rect,
            text='初始位置:',
            manager=self.ui_manager
        )
        
        # X位置
        pos_x_rect = pygame.Rect(self.sim_width + 10, y_start + 85, 80, 25)
        controls['pos_x_entry'] = pygame_gui.elements.UITextEntryLine(
            relative_rect=pos_x_rect,
            manager=self.ui_manager,
            initial_text=str(self.body_params[body_index]['pos_x'])
        )
        
        # Y位置
        pos_y_rect = pygame.Rect(self.sim_width + 100, y_start + 85, 80, 25)
        controls['pos_y_entry'] = pygame_gui.elements.UITextEntryLine(
            relative_rect=pos_y_rect,
            manager=self.ui_manager,
            initial_text=str(self.body_params[body_index]['pos_y'])
        )
        
        # 速度控制
        vel_label_rect = pygame.Rect(self.sim_width + 10, y_start + 120, 100, 20)
        controls['vel_label'] = pygame_gui.elements.UILabel(
            relative_rect=vel_label_rect,
            text='初始速度:',
            manager=self.ui_manager
        )
        
        # X速度
        vel_x_rect = pygame.Rect(self.sim_width + 10, y_start + 145, 80, 25)
        controls['vel_x_entry'] = pygame_gui.elements.UITextEntryLine(
            relative_rect=vel_x_rect,
            manager=self.ui_manager,
            initial_text=str(self.body_params[body_index]['vel_x'])
        )
        
        # Y速度
        vel_y_rect = pygame.Rect(self.sim_width + 100, y_start + 145, 80, 25)
        controls['vel_y_entry'] = pygame_gui.elements.UITextEntryLine(
            relative_rect=vel_y_rect,
            manager=self.ui_manager,
            initial_text=str(self.body_params[body_index]['vel_y'])
        )
        
        return controls
    
    def apply_preset_stable(self):
        """应用稳定轨道预设"""
        self.body_params = [
            {'mass': 100.0, 'pos_x': -1.0, 'pos_y': 0.0, 'vel_x': 0.0, 'vel_y': -0.2},
            {'mass': 100.0, 'pos_x': 1.0, 'pos_y': 0.0, 'vel_x': 0.0, 'vel_y': 0.2},
            {'mass': 1.0, 'pos_x': 0.0, 'pos_y': 0.0, 'vel_x': 0.5, 'vel_y': 0.0}
        ]
        self.gravity_constant = 1.0
        self.update_ui_from_params()
    
    def apply_preset_chaotic(self):
        """应用混沌轨道预设"""
        self.body_params = [
            {'mass': 150.0, 'pos_x': -2.0, 'pos_y': 0.0, 'vel_x': 0.1, 'vel_y': -0.4},
            {'mass': 30.0, 'pos_x': 2.0, 'pos_y': 0.0, 'vel_x': -0.2, 'vel_y': 0.8},
            {'mass': 5.0, 'pos_x': 0.0, 'pos_y': 3.0, 'vel_x': 0.6, 'vel_y': 0.1}
        ]
        self.gravity_constant = 0.8
        self.update_ui_from_params()
    
    def apply_preset_figure8(self):
        """应用8字轨道预设"""
        self.body_params = [
            {'mass': 50.0, 'pos_x': -0.97000436, 'pos_y': 0.24208753, 'vel_x': 0.4662036850, 'vel_y': 0.4323657300},
            {'mass': 50.0, 'pos_x': 0.97000436, 'pos_y': -0.24208753, 'vel_x': 0.4662036850, 'vel_y': 0.4323657300},
            {'mass': 50.0, 'pos_x': 0.0, 'pos_y': 0.0, 'vel_x': -0.93240737, 'vel_y': -0.86473146}
        ]
        self.gravity_constant = 1.0
        self.update_ui_from_params()
    
    def update_ui_from_params(self):
        """从参数更新UI控件"""
        self.gravity_slider.set_current_value(self.gravity_constant)
        self.gravity_value_label.set_text(f'{self.gravity_constant:.2f}')
        
        for i, controls in enumerate(self.body_controls):
            params = self.body_params[i]
            controls['mass_slider'].set_current_value(params['mass'])
            controls['pos_x_entry'].set_text(str(params['pos_x']))
            controls['pos_y_entry'].set_text(str(params['pos_y']))
            controls['vel_x_entry'].set_text(str(params['vel_x']))
            controls['vel_y_entry'].set_text(str(params['vel_y']))
            
            colors = ['红色质点', '蓝色质点', '绿色质点']
            controls['name_label'].set_text(f'{colors[i]} (质量: {params["mass"]:.1f})')
    
    def update_params_from_ui(self):
        """从UI控件更新参数"""
        self.gravity_constant = self.gravity_slider.get_current_value()
        self.gravity_value_label.set_text(f'{self.gravity_constant:.2f}')
        
        for i, controls in enumerate(self.body_controls):
            try:
                self.body_params[i]['mass'] = controls['mass_slider'].get_current_value()
                self.body_params[i]['pos_x'] = float(controls['pos_x_entry'].get_text())
                self.body_params[i]['pos_y'] = float(controls['pos_y_entry'].get_text())
                self.body_params[i]['vel_x'] = float(controls['vel_x_entry'].get_text())
                self.body_params[i]['vel_y'] = float(controls['vel_y_entry'].get_text())
                
                colors = ['红色质点', '蓝色质点', '绿色质点']
                controls['name_label'].set_text(f'{colors[i]} (质量: {self.body_params[i]["mass"]:.1f})')
            except ValueError:
                pass  # 忽略无效输入
    
    def reset_simulation(self):
        """根据当前参数重置模拟"""
        bodies = []
        for params in self.body_params:
            body = Body(
                mass=params['mass'],
                position=np.array([params['pos_x'], params['pos_y']]),
                velocity=np.array([params['vel_x'], params['vel_y']])
            )
            bodies.append(body)
        
        self.simulation = ThreeBodySimulation(bodies, G=self.gravity_constant)
    
    def world_to_screen(self, pos):
        """世界坐标转屏幕坐标"""
        x = int(pos[0] * self.scale + self.sim_width // 2)
        y = int(-pos[1] * self.scale + self.height // 2)
        return (x, y)
    
    def draw_simulation(self):
        """绘制模拟区域"""
        # 模拟区域背景
        sim_rect = pygame.Rect(0, 0, self.sim_width, self.height)
        pygame.draw.rect(self.screen, (20, 20, 40), sim_rect)
        
        # 绘制网格
        for x in range(0, self.sim_width, 50):
            pygame.draw.line(self.screen, (40, 40, 60), (x, 0), (x, self.height))
        for y in range(0, self.height, 50):
            pygame.draw.line(self.screen, (40, 40, 60), (0, y), (self.sim_width, y))
        
        # 绘制质点和轨迹
        colors = [(255, 100, 100), (100, 100, 255), (100, 255, 100)]
        
        for body, color in zip(self.simulation.bodies, colors):
            # 绘制轨迹
            if len(body.trajectory) > 1:
                trail_points = []
                for pos in body.trajectory[-self.trail_length:]:
                    screen_pos = self.world_to_screen(pos)
                    if 0 <= screen_pos[0] < self.sim_width and 0 <= screen_pos[1] < self.height:
                        trail_points.append(screen_pos)
                
                if len(trail_points) > 1:
                    pygame.draw.lines(self.screen, color, False, trail_points, 2)
            
            # 绘制质点
            screen_pos = self.world_to_screen(body.position)
            if 0 <= screen_pos[0] < self.sim_width and 0 <= screen_pos[1] < self.height:
                radius = max(5, int(np.log10(body.mass) * 4))
                pygame.draw.circle(self.screen, color, screen_pos, radius)
        
        # 分割线
        pygame.draw.line(self.screen, (100, 100, 100), (self.sim_width, 0), (self.sim_width, self.height), 2)
    
    def run(self):
        """游戏主循环"""
        while self.running:
            time_delta = self.clock.tick(60) / 1000.0
            
            for event in pygame.event.get():
                if event.type == pygame.QUIT:
                    self.running = False
                
                if event.type == pygame.USEREVENT:
                    if event.user_type == pygame_gui.UI_BUTTON_PRESSED:
                        if event.ui_element == self.start_button:
                            self.paused = False
                        elif event.ui_element == self.pause_button:
                            self.paused = True
                        elif event.ui_element == self.reset_button:
                            self.update_params_from_ui()
                            self.reset_simulation()
                        elif event.ui_element == self.stable_preset_button:
                            self.apply_preset_stable()
                            self.reset_simulation()
                        elif event.ui_element == self.chaotic_preset_button:
                            self.apply_preset_chaotic()
                            self.reset_simulation()
                        elif event.ui_element == self.figure8_preset_button:
                            self.apply_preset_figure8()
                            self.reset_simulation()
                
                if event.type == pygame.USEREVENT:
                    if event.user_type == pygame_gui.UI_HORIZONTAL_SLIDER_MOVED:
                        self.update_params_from_ui()
                
                self.ui_manager.process_events(event)
            
            self.ui_manager.update(time_delta)
            
            # 更新模拟
            if not self.paused and self.simulation:
                self.simulation.update_step()
            
            # 绘制
            self.screen.fill((60, 60, 80))  # 控制面板背景
            
            if self.simulation:
                self.draw_simulation()
            
            self.ui_manager.draw_ui(self.screen)
            
            pygame.display.flip()
        
        pygame.quit()

if __name__ == "__main__":
    game = InteractiveThreeBodyGame()
    game.run()
