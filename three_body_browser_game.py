# -*- coding: utf-8 -*-
"""
三体运动交互式游戏 - 浏览器版本
支持实时参数调节和多种预设轨道
"""

import http.server
import socketserver
import webbrowser
import os
import threading
import time

def create_html_game():
    """创建HTML游戏文件"""
    html_content = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>三体运动模拟游戏</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0c0c0c 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            overflow-x: auto;
        }
        
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        
        h1 {
            text-align: center;
            color: #00d4ff;
            text-shadow: 0 0 10px #00d4ff;
            margin-bottom: 30px;
            font-size: 2.5em;
        }
        
        .game-area {
            display: flex;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .canvas-container {
            flex: 1;
            min-width: 800px;
            background: rgba(0, 0, 0, 0.8);
            border: 2px solid #00d4ff;
            border-radius: 10px;
            padding: 10px;
        }
        
        canvas {
            width: 100%;
            height: 600px;
            background: radial-gradient(circle at center, #001122 0%, #000000 100%);
            border-radius: 5px;
        }
        
        .control-panel {
            width: 300px;
            background: rgba(0, 0, 0, 0.9);
            border: 2px solid #00d4ff;
            border-radius: 10px;
            padding: 20px;
            height: fit-content;
        }
        
        .control-group {
            margin-bottom: 20px;
        }
        
        .control-group h3 {
            color: #00d4ff;
            margin-bottom: 10px;
            border-bottom: 1px solid #00d4ff;
            padding-bottom: 5px;
        }
        
        .slider-container {
            margin-bottom: 15px;
        }
        
        .slider-container label {
            display: block;
            margin-bottom: 5px;
            color: #cccccc;
        }
        
        .slider-container input[type="range"] {
            width: 100%;
            height: 6px;
            background: #333;
            border-radius: 3px;
            outline: none;
        }
        
        .slider-container input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .value-display {
            color: #00d4ff;
            font-weight: bold;
            float: right;
        }
        
        .button-group {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 15px;
        }
        
        button {
            padding: 10px 15px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: all 0.3s;
            flex: 1;
            min-width: 80px;
        }
        
        button:hover {
            background: linear-gradient(45deg, #00ffff, #00ccff);
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 212, 255, 0.3);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .preset-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
        }
        
        .preset-button:hover {
            background: linear-gradient(45deg, #ff8e8e, #ff7575);
        }
        
        .info-panel {
            background: rgba(0, 0, 0, 0.7);
            padding: 15px;
            border-radius: 5px;
            margin-top: 20px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            line-height: 1.4;
        }
        
        .status {
            color: #00ff00;
            font-weight: bold;
        }
        
        .instructions {
            margin-top: 20px;
            padding: 15px;
            background: rgba(0, 100, 200, 0.2);
            border-radius: 5px;
            font-size: 12px;
            line-height: 1.6;
        }
        
        .instructions h4 {
            color: #00d4ff;
            margin-top: 0;
        }
        
        @media (max-width: 1200px) {
            .game-area {
                flex-direction: column;
            }
            
            .canvas-container {
                min-width: auto;
            }
            
            .control-panel {
                width: auto;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌌 三体运动模拟游戏 🌌</h1>
        
        <div class="game-area">
            <div class="canvas-container">
                <canvas id="gameCanvas"></canvas>
            </div>
            
            <div class="control-panel">
                <div class="control-group">
                    <h3>🎮 游戏控制</h3>
                    <div class="button-group">
                        <button id="startBtn" onclick="toggleSimulation()">开始</button>
                        <button onclick="resetSimulation()">重置</button>
                        <button onclick="clearTrails()">清除轨迹</button>
                    </div>
                </div>
                
                <div class="control-group">
                    <h3>⚙️ 物理参数</h3>
                    <div class="slider-container">
                        <label>引力常数 <span class="value-display" id="gravityValue">0.5</span></label>
                        <input type="range" id="gravitySlider" min="0.1" max="2.0" step="0.1" value="0.5" oninput="updateGravity(this.value)">
                    </div>
                    
                    <div class="slider-container">
                        <label>时间步长 <span class="value-display" id="timeStepValue">0.01</span></label>
                        <input type="range" id="timeStepSlider" min="0.005" max="0.05" step="0.005" value="0.01" oninput="updateTimeStep(this.value)">
                    </div>
                </div>
                
                <div class="control-group">
                    <h3>🪐 天体质量</h3>
                    <div class="slider-container">
                        <label>天体1 (红色) <span class="value-display" id="mass1Value">100</span></label>
                        <input type="range" id="mass1Slider" min="10" max="300" step="10" value="100" oninput="updateMass(0, this.value)">
                    </div>
                    
                    <div class="slider-container">
                        <label>天体2 (蓝色) <span class="value-display" id="mass2Value">50</span></label>
                        <input type="range" id="mass2Slider" min="10" max="300" step="10" value="50" oninput="updateMass(1, this.value)">
                    </div>
                    
                    <div class="slider-container">
                        <label>天体3 (绿色) <span class="value-display" id="mass3Value">10</span></label>
                        <input type="range" id="mass3Slider" min="1" max="100" step="1" value="10" oninput="updateMass(2, this.value)">
                    </div>
                </div>
                
                <div class="control-group">
                    <h3>🎯 预设轨道</h3>
                    <div class="button-group">
                        <button class="preset-button" onclick="applyPreset('stable')">稳定轨道</button>
                        <button class="preset-button" onclick="applyPreset('chaotic')">混沌轨道</button>
                        <button class="preset-button" onclick="applyPreset('binary')">双星系统</button>
                        <button class="preset-button" onclick="applyPreset('figure8')">8字轨道</button>
                    </div>
                </div>
                
                <div class="info-panel">
                    <div class="status">状态: <span id="statusText">暂停</span></div>
                    <div>时间: <span id="timeDisplay">0.00</span> 秒</div>
                    <div>帧率: <span id="fpsDisplay">0</span> FPS</div>
                    <div id="bodyInfo"></div>
                </div>
                
                <div class="instructions">
                    <h4>📖 操作说明</h4>
                    <p>• 点击"开始"按钮启动模拟</p>
                    <p>• 拖动滑块调节物理参数</p>
                    <p>• 选择预设轨道体验不同效果</p>
                    <p>• 鼠标点击画布可暂停/继续</p>
                    <p>• 观察三体在引力作用下的复杂运动</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 游戏状态
        let gameState = {
            running: false,
            time: 0,
            dt: 0.01,
            G: 0.5,
            scale: 60,
            centerX: 0,
            centerY: 0,
            lastTime: 0,
            fps: 0,
            frameCount: 0
        };

        // 三体数据
        let bodies = [
            {
                mass: 100,
                pos: [-2, 0],
                vel: [0, -0.3],
                trail: [],
                color: '#ff6666',
                name: '天体1'
            },
            {
                mass: 50,
                pos: [2, 0],
                vel: [0, 0.6],
                trail: [],
                color: '#6666ff',
                name: '天体2'
            },
            {
                mass: 10,
                pos: [0, 2],
                vel: [0.4, 0],
                trail: [],
                color: '#66ff66',
                name: '天体3'
            }
        ];

        // 获取画布和上下文
        const canvas = document.getElementById('gameCanvas');
        const ctx = canvas.getContext('2d');

        // 设置画布大小
        function resizeCanvas() {
            const container = canvas.parentElement;
            canvas.width = container.clientWidth - 20;
            canvas.height = 600;
            gameState.centerX = canvas.width / 2;
            gameState.centerY = canvas.height / 2;
        }

        // 初始化
        resizeCanvas();
        window.addEventListener('resize', resizeCanvas);

        // 画布点击事件
        canvas.addEventListener('click', function() {
            toggleSimulation();
        });

        // 计算引力
        function calculateForces() {
            const forces = bodies.map(() => [0, 0]);

            for (let i = 0; i < bodies.length; i++) {
                for (let j = 0; j < bodies.length; j++) {
                    if (i !== j) {
                        const dx = bodies[j].pos[0] - bodies[i].pos[0];
                        const dy = bodies[j].pos[1] - bodies[i].pos[1];
                        const r = Math.sqrt(dx * dx + dy * dy);

                        if (r > 1e-10) {
                            const forceMag = gameState.G * bodies[i].mass * bodies[j].mass / (r * r * r);
                            forces[i][0] += forceMag * dx;
                            forces[i][1] += forceMag * dy;
                        }
                    }
                }
            }

            return forces;
        }

        // 更新物理
        function updatePhysics() {
            if (!gameState.running) return;

            const forces = calculateForces();

            bodies.forEach((body, i) => {
                // 更新速度
                const ax = forces[i][0] / body.mass;
                const ay = forces[i][1] / body.mass;
                body.vel[0] += ax * gameState.dt;
                body.vel[1] += ay * gameState.dt;

                // 更新位置
                body.pos[0] += body.vel[0] * gameState.dt;
                body.pos[1] += body.vel[1] * gameState.dt;

                // 记录轨迹
                body.trail.push([...body.pos]);
                if (body.trail.length > 1000) {
                    body.trail.shift();
                }
            });

            gameState.time += gameState.dt;
        }

        // 绘制网格
        function drawGrid() {
            ctx.strokeStyle = 'rgba(0, 212, 255, 0.1)';
            ctx.lineWidth = 1;

            const gridSize = 50;

            for (let x = 0; x < canvas.width; x += gridSize) {
                ctx.beginPath();
                ctx.moveTo(x, 0);
                ctx.lineTo(x, canvas.height);
                ctx.stroke();
            }

            for (let y = 0; y < canvas.height; y += gridSize) {
                ctx.beginPath();
                ctx.moveTo(0, y);
                ctx.lineTo(canvas.width, y);
                ctx.stroke();
            }

            // 绘制中心十字
            ctx.strokeStyle = 'rgba(0, 212, 255, 0.3)';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.moveTo(gameState.centerX, 0);
            ctx.lineTo(gameState.centerX, canvas.height);
            ctx.moveTo(0, gameState.centerY);
            ctx.lineTo(canvas.width, gameState.centerY);
            ctx.stroke();
        }

        // 绘制轨迹
        function drawTrails() {
            bodies.forEach(body => {
                if (body.trail.length > 1) {
                    ctx.strokeStyle = body.color;
                    ctx.lineWidth = 2;
                    ctx.globalAlpha = 0.7;

                    ctx.beginPath();
                    for (let i = 0; i < body.trail.length; i++) {
                        const x = body.trail[i][0] * gameState.scale + gameState.centerX;
                        const y = -body.trail[i][1] * gameState.scale + gameState.centerY;

                        if (i === 0) {
                            ctx.moveTo(x, y);
                        } else {
                            ctx.lineTo(x, y);
                        }
                    }
                    ctx.stroke();
                    ctx.globalAlpha = 1;
                }
            });
        }

        // 绘制天体
        function drawBodies() {
            bodies.forEach(body => {
                const x = body.pos[0] * gameState.scale + gameState.centerX;
                const y = -body.pos[1] * gameState.scale + gameState.centerY;
                const radius = Math.max(5, Math.log10(body.mass) * 4);

                // 绘制光晕
                const gradient = ctx.createRadialGradient(x, y, 0, x, y, radius * 2);
                gradient.addColorStop(0, body.color);
                gradient.addColorStop(1, 'transparent');

                ctx.fillStyle = gradient;
                ctx.beginPath();
                ctx.arc(x, y, radius * 2, 0, 2 * Math.PI);
                ctx.fill();

                // 绘制天体
                ctx.fillStyle = body.color;
                ctx.beginPath();
                ctx.arc(x, y, radius, 0, 2 * Math.PI);
                ctx.fill();

                // 绘制边框
                ctx.strokeStyle = 'white';
                ctx.lineWidth = 1;
                ctx.stroke();

                // 绘制质量标签
                ctx.fillStyle = 'white';
                ctx.font = '12px Microsoft YaHei';
                ctx.fillText(`M=${body.mass}`, x + radius + 5, y - 5);
            });
        }

        // 主渲染函数
        function render() {
            // 清除画布
            ctx.fillStyle = 'rgba(0, 0, 0, 0.1)';
            ctx.fillRect(0, 0, canvas.width, canvas.height);

            // 绘制网格
            drawGrid();

            // 绘制轨迹
            drawTrails();

            // 绘制天体
            drawBodies();
        }

        // 更新信息显示
        function updateInfo() {
            document.getElementById('timeDisplay').textContent = gameState.time.toFixed(2);
            document.getElementById('statusText').textContent = gameState.running ? '运行中' : '暂停';
            document.getElementById('fpsDisplay').textContent = Math.round(gameState.fps);

            let bodyInfoText = '';
            bodies.forEach((body, i) => {
                bodyInfoText += `${body.name}: (${body.pos[0].toFixed(2)}, ${body.pos[1].toFixed(2)})<br>`;
            });
            document.getElementById('bodyInfo').innerHTML = bodyInfoText;
        }

        // 游戏主循环
        function gameLoop(currentTime) {
            // 计算FPS
            if (currentTime - gameState.lastTime >= 1000) {
                gameState.fps = gameState.frameCount;
                gameState.frameCount = 0;
                gameState.lastTime = currentTime;
            }
            gameState.frameCount++;

            // 更新物理
            updatePhysics();

            // 渲染
            render();

            // 更新信息
            updateInfo();

            // 继续循环
            requestAnimationFrame(gameLoop);
        }

        // 控制函数
        function toggleSimulation() {
            gameState.running = !gameState.running;
            document.getElementById('startBtn').textContent = gameState.running ? '暂停' : '开始';
        }

        function resetSimulation() {
            gameState.running = false;
            gameState.time = 0;
            document.getElementById('startBtn').textContent = '开始';

            // 重置天体状态
            bodies[0] = {mass: 100, pos: [-2, 0], vel: [0, -0.3], trail: [], color: '#ff6666', name: '天体1'};
            bodies[1] = {mass: 50, pos: [2, 0], vel: [0, 0.6], trail: [], color: '#6666ff', name: '天体2'};
            bodies[2] = {mass: 10, pos: [0, 2], vel: [0.4, 0], trail: [], color: '#66ff66', name: '天体3'};

            // 重置滑块
            document.getElementById('gravitySlider').value = 0.5;
            document.getElementById('timeStepSlider').value = 0.01;
            document.getElementById('mass1Slider').value = 100;
            document.getElementById('mass2Slider').value = 50;
            document.getElementById('mass3Slider').value = 10;

            updateGravity(0.5);
            updateTimeStep(0.01);
            updateMass(0, 100);
            updateMass(1, 50);
            updateMass(2, 10);
        }

        function clearTrails() {
            bodies.forEach(body => {
                body.trail = [];
            });
        }

        function updateGravity(value) {
            gameState.G = parseFloat(value);
            document.getElementById('gravityValue').textContent = value;
        }

        function updateTimeStep(value) {
            gameState.dt = parseFloat(value);
            document.getElementById('timeStepValue').textContent = value;
        }

        function updateMass(index, value) {
            bodies[index].mass = parseFloat(value);
            document.getElementById(`mass${index + 1}Value`).textContent = value;
        }

        function applyPreset(preset) {
            gameState.running = false;
            gameState.time = 0;
            document.getElementById('startBtn').textContent = '开始';

            switch (preset) {
                case 'stable':
                    bodies[0] = {mass: 100, pos: [-1, 0], vel: [0, -0.2], trail: [], color: '#ff6666', name: '天体1'};
                    bodies[1] = {mass: 100, pos: [1, 0], vel: [0, 0.2], trail: [], color: '#6666ff', name: '天体2'};
                    bodies[2] = {mass: 1, pos: [0, 0], vel: [0.5, 0], trail: [], color: '#66ff66', name: '天体3'};
                    gameState.G = 1.0;
                    gameState.dt = 0.01;
                    break;

                case 'chaotic':
                    bodies[0] = {mass: 150, pos: [-2, 0], vel: [0.1, -0.4], trail: [], color: '#ff6666', name: '天体1'};
                    bodies[1] = {mass: 30, pos: [2, 0], vel: [-0.2, 0.8], trail: [], color: '#6666ff', name: '天体2'};
                    bodies[2] = {mass: 5, pos: [0, 3], vel: [0.6, 0.1], trail: [], color: '#66ff66', name: '天体3'};
                    gameState.G = 0.8;
                    gameState.dt = 0.01;
                    break;

                case 'binary':
                    bodies[0] = {mass: 200, pos: [-1.5, 0], vel: [0, -0.3], trail: [], color: '#ff6666', name: '天体1'};
                    bodies[1] = {mass: 200, pos: [1.5, 0], vel: [0, 0.3], trail: [], color: '#6666ff', name: '天体2'};
                    bodies[2] = {mass: 5, pos: [0, 4], vel: [0.8, 0], trail: [], color: '#66ff66', name: '天体3'};
                    gameState.G = 1.2;
                    gameState.dt = 0.01;
                    break;

                case 'figure8':
                    bodies[0] = {mass: 100, pos: [-0.97000436, 0.24308753], vel: [0.466203685, 0.43236573], trail: [], color: '#ff6666', name: '天体1'};
                    bodies[1] = {mass: 100, pos: [0.97000436, -0.24308753], vel: [0.466203685, 0.43236573], trail: [], color: '#6666ff', name: '天体2'};
                    bodies[2] = {mass: 100, pos: [0, 0], vel: [-0.93240737, -0.86473146], trail: [], color: '#66ff66', name: '天体3'};
                    gameState.G = 1.0;
                    gameState.dt = 0.005;
                    break;
            }

            // 更新滑块
            document.getElementById('gravitySlider').value = gameState.G;
            document.getElementById('timeStepSlider').value = gameState.dt;
            document.getElementById('mass1Slider').value = bodies[0].mass;
            document.getElementById('mass2Slider').value = bodies[1].mass;
            document.getElementById('mass3Slider').value = bodies[2].mass;

            updateGravity(gameState.G);
            updateTimeStep(gameState.dt);
            updateMass(0, bodies[0].mass);
            updateMass(1, bodies[1].mass);
            updateMass(2, bodies[2].mass);
        }

        // 启动游戏
        gameLoop(0);

        // 显示欢迎信息
        console.log('🌌 三体运动模拟游戏已启动！');
        console.log('享受探索宇宙奥秘的乐趣吧！');
    </script>
</body>
</html>'''

    return html_content

def start_server():
    """启动本地服务器"""
    # 创建HTML文件
    html_content = create_html_game()
    
    # 使用UTF-8编码保存文件，确保中文正常显示
    with open('three_body_game.html', 'w', encoding='utf-8') as f:
        f.write(html_content)
    
    print("=== 三体运动模拟游戏 ===")
    print("正在启动本地服务器...")
    
    # 启动HTTP服务器
    PORT = 8000
    Handler = http.server.SimpleHTTPRequestHandler
    
    try:
        with socketserver.TCPServer(("", PORT), Handler) as httpd:
            print(f"服务器已启动: http://localhost:{PORT}")
            print(f"游戏地址: http://localhost:{PORT}/three_body_game.html")
            print("按 Ctrl+C 停止服务器")
            
            # 自动打开浏览器
            def open_browser():
                time.sleep(1)  # 等待服务器启动
                webbrowser.open(f'http://localhost:{PORT}/three_body_game.html')
            
            browser_thread = threading.Thread(target=open_browser)
            browser_thread.daemon = True
            browser_thread.start()
            
            # 启动服务器
            httpd.serve_forever()
            
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except OSError as e:
        if "Address already in use" in str(e):
            print(f"端口 {PORT} 已被占用，尝试使用其他端口...")
            # 尝试其他端口
            for port in range(8001, 8010):
                try:
                    with socketserver.TCPServer(("", port), Handler) as httpd:
                        print(f"服务器已启动: http://localhost:{port}")
                        print(f"游戏地址: http://localhost:{port}/three_body_game.html")
                        webbrowser.open(f'http://localhost:{port}/three_body_game.html')
                        httpd.serve_forever()
                        break
                except OSError:
                    continue
        else:
            print(f"启动服务器时出错: {e}")

def main():
    """主函数"""
    try:
        start_server()
    except Exception as e:
        print(f"程序运行出错: {e}")
        input("按回车键退出...")

if __name__ == "__main__":
    main()
