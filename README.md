# 🌌 高级3D三体运动模拟器

一个功能完整的3D三体运动模拟器，基于牛顿万有引力定律，提供沉浸式的科学探索体验。

![三体运动模拟器](https://img.shields.io/badge/三体模拟器-v1.0-blue) ![Python](https://img.shields.io/badge/Python-3.6+-green) ![Three.js](https://img.shields.io/badge/Three.js-r128-orange) ![License](https://img.shields.io/badge/License-MIT-yellow)

## ✨ 主要特性

### 🎮 核心功能
- **3D实时模拟**: 基于Three.js的高性能3D渲染
- **物理引擎**: 精确的万有引力计算和数值积分
- **交互控制**: 鼠标/键盘全方位视角控制
- **参数调节**: 实时调整物理参数观察效果

### 🔍 视图控制
- **自由视角**: 360度旋转、缩放、平移
- **快捷按钮**: 放大、缩小、重置视图、全屏模式
- **网格坐标**: 可切换的空间参考系

### 🌟 中心质心系统
- **引力约束**: 防止天体飞散到无穷远
- **可调质量**: 100-10000范围内调节
- **动态开关**: 实时启用/禁用功能

### 📊 物理参数显示
- **能量分析**: 动能、势能、总能量实时监测
- **角动量**: 系统角动量守恒验证
- **天体信息**: 位置、速度、质量、距离详细显示

### 💾 数据管理系统
- **轨迹录制**: 实时记录运动数据
- **数据保存**: JSON格式导出轨迹文件
- **轨迹加载**: 导入历史数据进行分析
- **回放控制**: 播放/暂停/快进/快退/时间轴跳转

### 🎯 预设配置
- **稳定轨道**: 相对稳定的三体运动
- **混沌轨道**: 展示混沌动力学特性
- **双星系统**: 双星+第三体配置
- **8字轨道**: 经典周期轨道
- **类太阳系**: 中心天体+行星系统

## 🚀 快速开始

### 环境要求
- Python 3.6+
- 现代浏览器 (Chrome 80+, Firefox 75+, Safari 13+, Edge 80+)
- 支持WebGL的显卡

### 安装运行

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd three-body-simulator
   ```

2. **启动服务器**
   ```bash
   python three_body_3d_advanced.py
   ```

3. **打开浏览器**
   ```
   http://localhost:8001/three_body_3d_advanced.html
   ```

4. **开始探索**
   - 点击"开始模拟"按钮
   - 使用鼠标控制视角
   - 调节参数观察效果

## 🎮 操作指南

### 基础操作
- **鼠标左键拖拽**: 旋转视角
- **鼠标滚轮**: 缩放视图
- **鼠标右键拖拽**: 平移视角

### 参数调节
- **引力常数**: 影响系统演化速度
- **时间步长**: 影响计算精度
- **天体质量**: 影响引力强度
- **中心质量**: 约束系统范围

### 高级功能
- **轨迹录制**: 开始录制 → 运行模拟 → 停止录制 → 保存轨迹
- **数据回放**: 加载轨迹 → 使用回放控制
- **预设体验**: 尝试不同的经典配置

## 📁 项目结构

```
三体运动模拟器/
├── three_body_browser_game.py          # 基础2D版本
├── three_body_3d_advanced.py           # 高级3D版本 ⭐
├── three_body_game.html                # 2D游戏页面
├── three_body_3d_advanced.html         # 3D游戏页面 ⭐
├── 三体运动模拟器项目文档.md           # 完整项目文档
└── README.md                           # 使用说明
```

## 🔬 科学原理

### 物理基础
- **万有引力定律**: F = G × m₁ × m₂ / r²
- **牛顿第二定律**: F = ma
- **能量守恒**: E = KE + PE = 常数
- **角动量守恒**: L = r × mv = 常数

### 数值方法
- **Verlet积分**: 保证数值稳定性
- **自适应步长**: 根据系统状态调整精度
- **误差控制**: 监测能量和角动量守恒

### 混沌理论
- **敏感依赖性**: 初始条件微小变化导致巨大差异
- **不可预测性**: 长期行为无法精确预测
- **确定性混沌**: 确定性系统的随机行为

## 🎓 教育价值

### 物理概念
- 直观理解万有引力和轨道力学
- 观察能量守恒和角动量守恒
- 体验混沌系统的复杂行为

### 数学概念
- 微分方程的数值解法
- 向量运算和3D几何
- 统计分析和数据处理

### 计算思维
- 物理建模和数值模拟
- 算法设计和性能优化
- 数据可视化和交互设计

## 🛠️ 技术栈

- **后端**: Python 3.x + HTTP服务器
- **前端**: HTML5 + CSS3 + JavaScript ES6
- **3D渲染**: Three.js (r128)
- **数据格式**: JSON
- **编码**: UTF-8

## 📊 性能指标

- **渲染帧率**: 60 FPS
- **计算精度**: 双精度浮点
- **内存使用**: < 100MB
- **轨迹容量**: 1000点/天体
- **数据大小**: ~1KB/秒录制

## 🔮 未来计划

### 功能扩展
- [ ] 多体系统 (4体、5体)
- [ ] 相对论效应
- [ ] 碰撞检测和合并
- [ ] 粒子系统效果

### 技术升级
- [ ] WebGL 2.0 渲染
- [ ] Web Workers 多线程
- [ ] WebXR VR/AR 支持
- [ ] 机器学习优化

## 🤝 贡献指南

欢迎贡献代码、报告问题或提出建议！

1. Fork 项目
2. 创建功能分支
3. 提交更改
4. 发起 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🙏 致谢

- **牛顿**: 万有引力定律
- **庞加莱**: 三体问题研究
- **刘慈欣**: 《三体》科幻小说
- **Three.js团队**: 优秀的3D图形库
- **开源社区**: 知识分享和技术支持

## 📞 联系方式

如有问题或建议，欢迎通过以下方式联系：

- 📧 邮箱: [<EMAIL>]
- 🐛 问题反馈: [GitHub Issues]
- 💬 讨论交流: [GitHub Discussions]

---

⭐ 如果这个项目对你有帮助，请给它一个星标！

🌌 **探索宇宙奥秘，从三体问题开始！**
