<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>高级3D三体运动模拟器</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/three@0.128.0/examples/js/controls/OrbitControls.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
            color: white;
            overflow: hidden;
        }
        
        #container {
            position: relative;
            width: 100vw;
            height: 100vh;
        }
        
        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }
        
        .ui-panel {
            position: absolute;
            background: rgba(0, 0, 0, 0.85);
            border: 2px solid #00d4ff;
            border-radius: 10px;
            padding: 15px;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }
        
        .title-panel {
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }
        
        .title-panel h1 {
            color: #00d4ff;
            text-shadow: 0 0 10px #00d4ff;
            font-size: 2em;
            margin-bottom: 10px;
        }
        
        .control-panel {
            top: 20px;
            left: 20px;
            width: 320px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .info-panel {
            top: 20px;
            right: 20px;
            width: 300px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }
        
        .view-controls {
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
        }
        
        .playback-controls {
            bottom: 20px;
            right: 20px;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .control-group {
            margin-bottom: 20px;
            border-bottom: 1px solid #333;
            padding-bottom: 15px;
        }
        
        .control-group:last-child {
            border-bottom: none;
        }
        
        .control-group h3 {
            color: #00d4ff;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .slider-container {
            margin-bottom: 10px;
        }
        
        .slider-container label {
            display: block;
            margin-bottom: 5px;
            color: #cccccc;
            font-size: 0.9em;
        }
        
        .slider-container input[type="range"] {
            width: 100%;
            height: 6px;
            background: #333;
            border-radius: 3px;
            outline: none;
            cursor: pointer;
        }
        
        .slider-container input[type="range"]::-webkit-slider-thumb {
            appearance: none;
            width: 16px;
            height: 16px;
            background: #00d4ff;
            border-radius: 50%;
            cursor: pointer;
        }
        
        .value-display {
            color: #00d4ff;
            font-weight: bold;
            float: right;
        }
        
        button {
            padding: 8px 12px;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s;
            margin: 2px;
        }
        
        button:hover {
            background: linear-gradient(45deg, #00ffff, #00ccff);
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0, 212, 255, 0.3);
        }
        
        .preset-button {
            background: linear-gradient(45deg, #ff6b6b, #ee5a52);
            width: 100%;
            margin-bottom: 5px;
        }
        
        .preset-button:hover {
            background: linear-gradient(45deg, #ff8e8e, #ff7575);
        }
        
        .view-button {
            background: linear-gradient(45deg, #4ecdc4, #44a08d);
            padding: 10px 15px;
            font-size: 14px;
        }
        
        .view-button:hover {
            background: linear-gradient(45deg, #6ee5dd, #5cb3a7);
        }
        
        .info-item {
            margin-bottom: 8px;
            font-size: 0.85em;
            line-height: 1.4;
        }
        
        .info-label {
            color: #00d4ff;
            font-weight: bold;
        }
        
        .info-value {
            color: #ffffff;
        }
        
        .physics-data {
            background: rgba(0, 50, 100, 0.3);
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 10px;
        }
        
        .body-info {
            background: rgba(50, 0, 50, 0.3);
            padding: 8px;
            border-radius: 5px;
            margin-bottom: 8px;
            border-left: 3px solid;
        }
        
        .body-info.body-0 { border-left-color: #ff6666; }
        .body-info.body-1 { border-left-color: #6666ff; }
        .body-info.body-2 { border-left-color: #66ff66; }
        .body-info.body-3 { border-left-color: #ffff66; }
        
        .checkbox-container {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .checkbox-container input[type="checkbox"] {
            margin-right: 8px;
        }
        
        .file-controls {
            display: flex;
            gap: 5px;
            margin-top: 10px;
        }
        
        .file-controls input[type="file"] {
            display: none;
        }
        
        .file-controls label {
            background: linear-gradient(45deg, #9b59b6, #8e44ad);
            color: white;
            padding: 6px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 11px;
        }
        
        .file-controls label:hover {
            background: linear-gradient(45deg, #b370cf, #a569bd);
        }
        
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-running { background: #00ff00; }
        .status-paused { background: #ffff00; }
        .status-recording { background: #ff0000; }
        .status-playing { background: #00ffff; }
        
        @media (max-width: 1200px) {
            .control-panel, .info-panel {
                width: 280px;
            }
            
            .title-panel h1 {
                font-size: 1.5em;
            }
        }
        
        @media (max-width: 768px) {
            .control-panel {
                width: calc(100vw - 40px);
                max-width: 300px;
            }
            
            .info-panel {
                display: none;
            }
            
            .view-controls {
                bottom: 80px;
            }
        }
    </style>
</head>
<body>
    <div id="container">
        <div id="canvas-container"></div>
        
        <!-- 标题面板 -->
        <div class="ui-panel title-panel">
            <h1>🌌 高级3D三体运动模拟器 🌌</h1>
            <div>
                <span class="status-indicator" id="statusIndicator"></span>
                <span id="statusText">已就绪</span>
            </div>
        </div>
        
        <!-- 控制面板 -->
        <div class="ui-panel control-panel">
            <div class="control-group">
                <h3>🎮 模拟控制</h3>
                <button id="startBtn" onclick="toggleSimulation()">开始模拟</button>
                <button onclick="resetSimulation()">重置</button>
                <button onclick="clearTrails()">清除轨迹</button>
                
                <div class="checkbox-container">
                    <input type="checkbox" id="showTrails" checked onchange="toggleTrails()">
                    <label for="showTrails">显示轨迹</label>
                </div>
                
                <div class="checkbox-container">
                    <input type="checkbox" id="showGrid" checked onchange="toggleGrid()">
                    <label for="showGrid">显示网格</label>
                </div>
                
                <div class="checkbox-container">
                    <input type="checkbox" id="enableCentralMass" onchange="toggleCentralMass()">
                    <label for="enableCentralMass">启用中心质心</label>
                </div>
            </div>
            
            <div class="control-group">
                <h3>⚙️ 物理参数</h3>
                <div class="slider-container">
                    <label>引力常数 <span class="value-display" id="gravityValue">1.0</span></label>
                    <input type="range" id="gravitySlider" min="0.1" max="5.0" step="0.1" value="1.0" oninput="updateGravity(this.value)">
                </div>
                
                <div class="slider-container">
                    <label>时间步长 <span class="value-display" id="timeStepValue">0.01</span></label>
                    <input type="range" id="timeStepSlider" min="0.001" max="0.05" step="0.001" value="0.01" oninput="updateTimeStep(this.value)">
                </div>
                
                <div class="slider-container">
                    <label>中心质量 <span class="value-display" id="centralMassValue">1000</span></label>
                    <input type="range" id="centralMassSlider" min="100" max="10000" step="100" value="1000" oninput="updateCentralMass(this.value)">
                </div>
            </div>
            
            <div class="control-group">
                <h3>🪐 天体质量</h3>
                <div class="slider-container">
                    <label>天体1 (红) <span class="value-display" id="mass1Value">100</span></label>
                    <input type="range" id="mass1Slider" min="10" max="500" step="10" value="100" oninput="updateMass(0, this.value)">
                </div>
                
                <div class="slider-container">
                    <label>天体2 (蓝) <span class="value-display" id="mass2Value">100</span></label>
                    <input type="range" id="mass2Slider" min="10" max="500" step="10" value="100" oninput="updateMass(1, this.value)">
                </div>
                
                <div class="slider-container">
                    <label>天体3 (绿) <span class="value-display" id="mass3Value">100</span></label>
                    <input type="range" id="mass3Slider" min="10" max="500" step="10" value="100" oninput="updateMass(2, this.value)">
                </div>
            </div>
            
            <div class="control-group">
                <h3>🎯 预设配置</h3>
                <button class="preset-button" onclick="applyPreset('stable')">稳定轨道</button>
                <button class="preset-button" onclick="applyPreset('chaotic')">混沌轨道</button>
                <button class="preset-button" onclick="applyPreset('binary')">双星系统</button>
                <button class="preset-button" onclick="applyPreset('figure8')">8字轨道</button>
                <button class="preset-button" onclick="applyPreset('solar')">类太阳系</button>
            </div>
            
            <div class="control-group">
                <h3>💾 数据管理</h3>
                <button onclick="startRecording()">开始录制</button>
                <button onclick="stopRecording()">停止录制</button>
                <button onclick="saveTrajectory()">保存轨迹</button>
                
                <div class="file-controls">
                    <label for="loadFile">加载轨迹</label>
                    <input type="file" id="loadFile" accept=".json" onchange="loadTrajectory(event)">
                    <button onclick="exportData()">导出数据</button>
                </div>
            </div>
        </div>

        <!-- 信息面板 -->
        <div class="ui-panel info-panel">
            <div class="control-group">
                <h3>📊 系统状态</h3>
                <div class="physics-data">
                    <div class="info-item">
                        <span class="info-label">时间:</span>
                        <span class="info-value" id="timeDisplay">0.00 s</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">帧率:</span>
                        <span class="info-value" id="fpsDisplay">0 FPS</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">总能量:</span>
                        <span class="info-value" id="totalEnergyDisplay">0.00 J</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">动能:</span>
                        <span class="info-value" id="kineticEnergyDisplay">0.00 J</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">势能:</span>
                        <span class="info-value" id="potentialEnergyDisplay">0.00 J</span>
                    </div>
                    <div class="info-item">
                        <span class="info-label">角动量:</span>
                        <span class="info-value" id="angularMomentumDisplay">0.00</span>
                    </div>
                </div>
            </div>

            <div class="control-group">
                <h3>🪐 天体信息</h3>
                <div id="bodyInfoContainer"></div>
            </div>

            <div class="control-group">
                <h3>📹 录制状态</h3>
                <div class="info-item">
                    <span class="info-label">录制时长:</span>
                    <span class="info-value" id="recordingTime">0.00 s</span>
                </div>
                <div class="info-item">
                    <span class="info-label">数据点数:</span>
                    <span class="info-value" id="dataPoints">0</span>
                </div>
                <div class="info-item">
                    <span class="info-label">文件大小:</span>
                    <span class="info-value" id="fileSize">0 KB</span>
                </div>
            </div>
        </div>

        <!-- 视图控制 -->
        <div class="ui-panel view-controls">
            <button class="view-button" onclick="zoomIn()">🔍 放大</button>
            <button class="view-button" onclick="zoomOut()">🔍 缩小</button>
            <button class="view-button" onclick="resetView()">🎯 重置视图</button>
            <button class="view-button" onclick="toggleFullscreen()">📺 全屏</button>
        </div>

        <!-- 回放控制 -->
        <div class="ui-panel playback-controls">
            <button onclick="playbackStart()">⏮️</button>
            <button onclick="playbackPrev()">⏪</button>
            <button id="playbackBtn" onclick="togglePlayback()">▶️</button>
            <button onclick="playbackNext()">⏩</button>
            <button onclick="playbackEnd()">⏭️</button>
            <input type="range" id="playbackSlider" min="0" max="100" value="0" style="width: 200px; margin: 0 10px;">
            <span id="playbackTime">0.00s</span>
        </div>
    </div>

    <script>
        // 全局变量
        let scene, camera, renderer, controls;
        let bodies = [];
        let centralMass = null;
        let trails = [];
        let gridHelper, axesHelper;
        let gameState = {
            running: false,
            recording: false,
            playback: false,
            time: 0,
            dt: 0.01,
            G: 1.0,
            centralMassValue: 1000,
            enableCentralMass: false,
            showTrails: true,
            showGrid: true,
            fps: 0,
            frameCount: 0,
            lastTime: 0
        };
        let recordedData = [];
        let playbackData = [];
        let playbackIndex = 0;

        // 初始化3D场景
        function initScene() {
            // 创建场景
            scene = new THREE.Scene();
            scene.background = new THREE.Color(0x000011);

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 10000);
            camera.position.set(50, 50, 50);

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.shadowMap.enabled = true;
            renderer.shadowMap.type = THREE.PCFSoftShadowMap;
            document.getElementById('canvas-container').appendChild(renderer.domElement);

            // 创建控制器
            controls = new THREE.OrbitControls(camera, renderer.domElement);
            controls.enableDamping = true;
            controls.dampingFactor = 0.05;
            controls.enableZoom = true;
            controls.enablePan = true;

            // 添加光源
            const ambientLight = new THREE.AmbientLight(0x404040, 0.3);
            scene.add(ambientLight);

            const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);
            directionalLight.position.set(100, 100, 100);
            directionalLight.castShadow = true;
            directionalLight.shadow.mapSize.width = 2048;
            directionalLight.shadow.mapSize.height = 2048;
            scene.add(directionalLight);

            // 创建网格
            gridHelper = new THREE.GridHelper(200, 20, 0x00d4ff, 0x333333);
            scene.add(gridHelper);

            // 创建坐标轴
            axesHelper = new THREE.AxesHelper(50);
            scene.add(axesHelper);

            // 初始化天体
            initBodies();

            // 窗口大小调整
            window.addEventListener('resize', onWindowResize);
        }

        // 初始化天体
        function initBodies() {
            // 清除现有天体
            bodies.forEach(body => {
                scene.remove(body.mesh);
                if (body.trail) scene.remove(body.trail);
            });
            bodies = [];
            trails = [];

            // 创建三个天体
            const bodyData = [
                { mass: 100, pos: [-10, 0, 0], vel: [0, 0, 5], color: 0xff6666, name: '天体1' },
                { mass: 100, pos: [10, 0, 0], vel: [0, 0, -5], color: 0x6666ff, name: '天体2' },
                { mass: 100, pos: [0, 0, 15], vel: [3, 0, 0], color: 0x66ff66, name: '天体3' }
            ];

            bodyData.forEach((data, index) => {
                const body = createBody(data.mass, data.pos, data.vel, data.color, data.name);
                bodies.push(body);
            });

            // 创建中心质心
            createCentralMass();
        }

        // 创建天体
        function createBody(mass, position, velocity, color, name) {
            const radius = Math.max(1, Math.log10(mass) * 0.8);

            // 创建几何体和材质
            const geometry = new THREE.SphereGeometry(radius, 32, 32);
            const material = new THREE.MeshPhongMaterial({
                color: color,
                emissive: color,
                emissiveIntensity: 0.2,
                shininess: 100
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(position[0], position[1], position[2]);
            mesh.castShadow = true;
            mesh.receiveShadow = true;
            scene.add(mesh);

            // 创建光晕效果
            const glowGeometry = new THREE.SphereGeometry(radius * 2, 16, 16);
            const glowMaterial = new THREE.MeshBasicMaterial({
                color: color,
                transparent: true,
                opacity: 0.3
            });
            const glow = new THREE.Mesh(glowGeometry, glowMaterial);
            mesh.add(glow);

            // 创建轨迹
            const trailGeometry = new THREE.BufferGeometry();
            const trailMaterial = new THREE.LineBasicMaterial({
                color: color,
                transparent: true,
                opacity: 0.7,
                linewidth: 2
            });
            const trail = new THREE.Line(trailGeometry, trailMaterial);
            scene.add(trail);

            return {
                mass: mass,
                pos: [...position],
                vel: [...velocity],
                mesh: mesh,
                trail: trail,
                trailPoints: [],
                color: color,
                name: name,
                radius: radius
            };
        }

        // 创建中心质心
        function createCentralMass() {
            if (centralMass) {
                scene.remove(centralMass.mesh);
            }

            const radius = 3;
            const geometry = new THREE.SphereGeometry(radius, 32, 32);
            const material = new THREE.MeshPhongMaterial({
                color: 0xffff00,
                emissive: 0xffff00,
                emissiveIntensity: 0.5
            });

            const mesh = new THREE.Mesh(geometry, material);
            mesh.position.set(0, 0, 0);
            mesh.visible = gameState.enableCentralMass;
            scene.add(mesh);

            centralMass = {
                mass: gameState.centralMassValue,
                pos: [0, 0, 0],
                mesh: mesh
            };
        }

        // 计算引力
        function calculateForces() {
            const forces = bodies.map(() => [0, 0, 0]);

            // 天体间相互作用
            for (let i = 0; i < bodies.length; i++) {
                for (let j = 0; j < bodies.length; j++) {
                    if (i !== j) {
                        const dx = bodies[j].pos[0] - bodies[i].pos[0];
                        const dy = bodies[j].pos[1] - bodies[i].pos[1];
                        const dz = bodies[j].pos[2] - bodies[i].pos[2];
                        const r = Math.sqrt(dx*dx + dy*dy + dz*dz);

                        if (r > 1e-10) {
                            const forceMag = gameState.G * bodies[i].mass * bodies[j].mass / (r*r*r);
                            forces[i][0] += forceMag * dx;
                            forces[i][1] += forceMag * dy;
                            forces[i][2] += forceMag * dz;
                        }
                    }
                }

                // 中心质心的引力
                if (gameState.enableCentralMass && centralMass) {
                    const dx = centralMass.pos[0] - bodies[i].pos[0];
                    const dy = centralMass.pos[1] - bodies[i].pos[1];
                    const dz = centralMass.pos[2] - bodies[i].pos[2];
                    const r = Math.sqrt(dx*dx + dy*dy + dz*dz);

                    if (r > 1e-10) {
                        const forceMag = gameState.G * bodies[i].mass * centralMass.mass / (r*r*r);
                        forces[i][0] += forceMag * dx;
                        forces[i][1] += forceMag * dy;
                        forces[i][2] += forceMag * dz;
                    }
                }
            }

            return forces;
        }

        // 更新物理
        function updatePhysics() {
            if (!gameState.running || gameState.playback) return;

            const forces = calculateForces();

            bodies.forEach((body, i) => {
                // 更新速度
                const ax = forces[i][0] / body.mass;
                const ay = forces[i][1] / body.mass;
                const az = forces[i][2] / body.mass;

                body.vel[0] += ax * gameState.dt;
                body.vel[1] += ay * gameState.dt;
                body.vel[2] += az * gameState.dt;

                // 更新位置
                body.pos[0] += body.vel[0] * gameState.dt;
                body.pos[1] += body.vel[1] * gameState.dt;
                body.pos[2] += body.vel[2] * gameState.dt;

                // 更新网格位置
                body.mesh.position.set(body.pos[0], body.pos[1], body.pos[2]);

                // 更新轨迹
                if (gameState.showTrails) {
                    body.trailPoints.push(new THREE.Vector3(body.pos[0], body.pos[1], body.pos[2]));
                    if (body.trailPoints.length > 1000) {
                        body.trailPoints.shift();
                    }

                    const positions = new Float32Array(body.trailPoints.length * 3);
                    body.trailPoints.forEach((point, index) => {
                        positions[index * 3] = point.x;
                        positions[index * 3 + 1] = point.y;
                        positions[index * 3 + 2] = point.z;
                    });

                    body.trail.geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
                    body.trail.geometry.attributes.position.needsUpdate = true;
                }
            });

            gameState.time += gameState.dt;

            // 录制数据
            if (gameState.recording) {
                recordFrame();
            }
        }

        // 计算物理量
        function calculatePhysics() {
            let totalKineticEnergy = 0;
            let totalPotentialEnergy = 0;
            let totalAngularMomentum = [0, 0, 0];

            // 计算动能和角动量
            bodies.forEach(body => {
                const vSq = body.vel[0]*body.vel[0] + body.vel[1]*body.vel[1] + body.vel[2]*body.vel[2];
                totalKineticEnergy += 0.5 * body.mass * vSq;

                // 角动量 L = r × mv
                const r = [body.pos[0], body.pos[1], body.pos[2]];
                const mv = [body.mass * body.vel[0], body.mass * body.vel[1], body.mass * body.vel[2]];

                totalAngularMomentum[0] += r[1] * mv[2] - r[2] * mv[1];
                totalAngularMomentum[1] += r[2] * mv[0] - r[0] * mv[2];
                totalAngularMomentum[2] += r[0] * mv[1] - r[1] * mv[0];
            });

            // 计算势能
            for (let i = 0; i < bodies.length; i++) {
                for (let j = i + 1; j < bodies.length; j++) {
                    const dx = bodies[j].pos[0] - bodies[i].pos[0];
                    const dy = bodies[j].pos[1] - bodies[i].pos[1];
                    const dz = bodies[j].pos[2] - bodies[i].pos[2];
                    const r = Math.sqrt(dx*dx + dy*dy + dz*dz);

                    if (r > 1e-10) {
                        totalPotentialEnergy -= gameState.G * bodies[i].mass * bodies[j].mass / r;
                    }
                }

                // 中心质心的势能
                if (gameState.enableCentralMass && centralMass) {
                    const dx = centralMass.pos[0] - bodies[i].pos[0];
                    const dy = centralMass.pos[1] - bodies[i].pos[1];
                    const dz = centralMass.pos[2] - bodies[i].pos[2];
                    const r = Math.sqrt(dx*dx + dy*dy + dz*dz);

                    if (r > 1e-10) {
                        totalPotentialEnergy -= gameState.G * bodies[i].mass * centralMass.mass / r;
                    }
                }
            }

            const angularMagnitude = Math.sqrt(
                totalAngularMomentum[0]*totalAngularMomentum[0] +
                totalAngularMomentum[1]*totalAngularMomentum[1] +
                totalAngularMomentum[2]*totalAngularMomentum[2]
            );

            return {
                kineticEnergy: totalKineticEnergy,
                potentialEnergy: totalPotentialEnergy,
                totalEnergy: totalKineticEnergy + totalPotentialEnergy,
                angularMomentum: angularMagnitude
            };
        }

        // 更新信息显示
        function updateInfo() {
            const physics = calculatePhysics();

            document.getElementById('timeDisplay').textContent = gameState.time.toFixed(2) + ' s';
            document.getElementById('fpsDisplay').textContent = Math.round(gameState.fps) + ' FPS';
            document.getElementById('totalEnergyDisplay').textContent = physics.totalEnergy.toFixed(2) + ' J';
            document.getElementById('kineticEnergyDisplay').textContent = physics.kineticEnergy.toFixed(2) + ' J';
            document.getElementById('potentialEnergyDisplay').textContent = physics.potentialEnergy.toFixed(2) + ' J';
            document.getElementById('angularMomentumDisplay').textContent = physics.angularMomentum.toFixed(2);

            // 更新天体信息
            const container = document.getElementById('bodyInfoContainer');
            container.innerHTML = '';

            bodies.forEach((body, index) => {
                const div = document.createElement('div');
                div.className = `body-info body-${index}`;

                const speed = Math.sqrt(body.vel[0]*body.vel[0] + body.vel[1]*body.vel[1] + body.vel[2]*body.vel[2]);
                const distance = Math.sqrt(body.pos[0]*body.pos[0] + body.pos[1]*body.pos[1] + body.pos[2]*body.pos[2]);

                div.innerHTML = `
                    <strong>${body.name}</strong><br>
                    质量: ${body.mass}<br>
                    位置: (${body.pos[0].toFixed(1)}, ${body.pos[1].toFixed(1)}, ${body.pos[2].toFixed(1)})<br>
                    速度: ${speed.toFixed(2)} m/s<br>
                    距离中心: ${distance.toFixed(2)} m
                `;

                container.appendChild(div);
            });

            // 更新录制信息
            if (gameState.recording) {
                document.getElementById('recordingTime').textContent = gameState.time.toFixed(2) + ' s';
                document.getElementById('dataPoints').textContent = recordedData.length;
                document.getElementById('fileSize').textContent = Math.round(JSON.stringify(recordedData).length / 1024) + ' KB';
            }

            // 更新状态指示器
            const indicator = document.getElementById('statusIndicator');
            const statusText = document.getElementById('statusText');

            if (gameState.playback) {
                indicator.className = 'status-indicator status-playing';
                statusText.textContent = '回放中';
            } else if (gameState.recording) {
                indicator.className = 'status-indicator status-recording';
                statusText.textContent = '录制中';
            } else if (gameState.running) {
                indicator.className = 'status-indicator status-running';
                statusText.textContent = '运行中';
            } else {
                indicator.className = 'status-indicator status-paused';
                statusText.textContent = '暂停';
            }
        }

        // 主渲染循环
        function animate(currentTime) {
            // 计算FPS
            if (currentTime - gameState.lastTime >= 1000) {
                gameState.fps = gameState.frameCount;
                gameState.frameCount = 0;
                gameState.lastTime = currentTime;
            }
            gameState.frameCount++;

            // 更新物理
            updatePhysics();

            // 更新控制器
            controls.update();

            // 渲染场景
            renderer.render(scene, camera);

            // 更新信息
            updateInfo();

            requestAnimationFrame(animate);
        }

        // 窗口大小调整
        function onWindowResize() {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        }

        // 控制函数
        function toggleSimulation() {
            if (gameState.playback) {
                gameState.playback = false;
                document.getElementById('playbackBtn').textContent = '▶️';
            }

            gameState.running = !gameState.running;
            document.getElementById('startBtn').textContent = gameState.running ? '暂停模拟' : '开始模拟';
        }

        function resetSimulation() {
            gameState.running = false;
            gameState.playback = false;
            gameState.recording = false;
            gameState.time = 0;
            recordedData = [];
            playbackData = [];

            document.getElementById('startBtn').textContent = '开始模拟';
            document.getElementById('playbackBtn').textContent = '▶️';

            initBodies();

            // 重置滑块
            document.getElementById('gravitySlider').value = 1.0;
            document.getElementById('timeStepSlider').value = 0.01;
            document.getElementById('centralMassSlider').value = 1000;
            document.getElementById('mass1Slider').value = 100;
            document.getElementById('mass2Slider').value = 100;
            document.getElementById('mass3Slider').value = 100;

            updateGravity(1.0);
            updateTimeStep(0.01);
            updateCentralMass(1000);
            updateMass(0, 100);
            updateMass(1, 100);
            updateMass(2, 100);
        }

        function clearTrails() {
            bodies.forEach(body => {
                body.trailPoints = [];
                body.trail.geometry.setAttribute('position', new THREE.BufferAttribute(new Float32Array(0), 3));
            });
        }

        function updateGravity(value) {
            gameState.G = parseFloat(value);
            document.getElementById('gravityValue').textContent = value;
        }

        function updateTimeStep(value) {
            gameState.dt = parseFloat(value);
            document.getElementById('timeStepValue').textContent = value;
        }

        function updateCentralMass(value) {
            gameState.centralMassValue = parseFloat(value);
            if (centralMass) {
                centralMass.mass = gameState.centralMassValue;
            }
            document.getElementById('centralMassValue').textContent = value;
        }

        function updateMass(index, value) {
            if (bodies[index]) {
                bodies[index].mass = parseFloat(value);
            }
            document.getElementById(`mass${index + 1}Value`).textContent = value;
        }

        function toggleTrails() {
            gameState.showTrails = document.getElementById('showTrails').checked;
            bodies.forEach(body => {
                body.trail.visible = gameState.showTrails;
            });
        }

        function toggleGrid() {
            gameState.showGrid = document.getElementById('showGrid').checked;
            gridHelper.visible = gameState.showGrid;
            axesHelper.visible = gameState.showGrid;
        }

        function toggleCentralMass() {
            gameState.enableCentralMass = document.getElementById('enableCentralMass').checked;
            if (centralMass) {
                centralMass.mesh.visible = gameState.enableCentralMass;
            }
        }

        // 预设配置
        function applyPreset(preset) {
            resetSimulation();

            switch (preset) {
                case 'stable':
                    bodies[0].pos = [-5, 0, 0]; bodies[0].vel = [0, 0, 3]; bodies[0].mass = 100;
                    bodies[1].pos = [5, 0, 0]; bodies[1].vel = [0, 0, -3]; bodies[1].mass = 100;
                    bodies[2].pos = [0, 0, 8]; bodies[2].vel = [2, 0, 0]; bodies[2].mass = 50;
                    gameState.G = 1.5;
                    break;

                case 'chaotic':
                    bodies[0].pos = [-8, 0, 0]; bodies[0].vel = [0.5, 0, -2]; bodies[0].mass = 150;
                    bodies[1].pos = [8, 0, 0]; bodies[1].vel = [-0.5, 0, 4]; bodies[1].mass = 80;
                    bodies[2].pos = [0, 0, 12]; bodies[2].vel = [3, 0, 0.5]; bodies[2].mass = 30;
                    gameState.G = 1.2;
                    break;

                case 'binary':
                    bodies[0].pos = [-6, 0, 0]; bodies[0].vel = [0, 0, -2]; bodies[0].mass = 200;
                    bodies[1].pos = [6, 0, 0]; bodies[1].vel = [0, 0, 2]; bodies[1].mass = 200;
                    bodies[2].pos = [0, 0, 15]; bodies[2].vel = [4, 0, 0]; bodies[2].mass = 20;
                    gameState.G = 2.0;
                    break;

                case 'figure8':
                    bodies[0].pos = [-4.85, 0, 1.22]; bodies[0].vel = [2.33, 0, 2.16]; bodies[0].mass = 100;
                    bodies[1].pos = [4.85, 0, -1.22]; bodies[1].vel = [2.33, 0, 2.16]; bodies[1].mass = 100;
                    bodies[2].pos = [0, 0, 0]; bodies[2].vel = [-4.66, 0, -4.32]; bodies[2].mass = 100;
                    gameState.G = 1.0;
                    gameState.dt = 0.005;
                    break;

                case 'solar':
                    bodies[0].pos = [0, 0, 0]; bodies[0].vel = [0, 0, 0]; bodies[0].mass = 500;
                    bodies[1].pos = [15, 0, 0]; bodies[1].vel = [0, 0, 3]; bodies[1].mass = 50;
                    bodies[2].pos = [25, 0, 0]; bodies[2].vel = [0, 0, 2.5]; bodies[2].mass = 30;
                    gameState.G = 2.5;
                    gameState.enableCentralMass = true;
                    gameState.centralMassValue = 2000;
                    break;
            }

            // 更新天体位置和属性
            bodies.forEach((body, index) => {
                body.mesh.position.set(body.pos[0], body.pos[1], body.pos[2]);
                body.trailPoints = [];

                // 更新滑块
                document.getElementById(`mass${index + 1}Slider`).value = body.mass;
                updateMass(index, body.mass);
            });

            // 更新其他参数
            document.getElementById('gravitySlider').value = gameState.G;
            document.getElementById('timeStepSlider').value = gameState.dt;
            document.getElementById('centralMassSlider').value = gameState.centralMassValue;
            document.getElementById('enableCentralMass').checked = gameState.enableCentralMass;

            updateGravity(gameState.G);
            updateTimeStep(gameState.dt);
            updateCentralMass(gameState.centralMassValue);
            toggleCentralMass();
        }

        // 录制功能
        function startRecording() {
            if (!gameState.recording) {
                gameState.recording = true;
                recordedData = [];
                console.log('开始录制轨迹数据');
            }
        }

        function stopRecording() {
            if (gameState.recording) {
                gameState.recording = false;
                console.log(`录制完成，共 ${recordedData.length} 帧数据`);
            }
        }

        function recordFrame() {
            const frame = {
                time: gameState.time,
                bodies: bodies.map(body => ({
                    pos: [...body.pos],
                    vel: [...body.vel],
                    mass: body.mass
                })),
                physics: calculatePhysics()
            };
            recordedData.push(frame);
        }

        function saveTrajectory() {
            if (recordedData.length === 0) {
                alert('没有录制数据可保存');
                return;
            }

            const data = {
                metadata: {
                    version: '1.0',
                    timestamp: new Date().toISOString(),
                    duration: gameState.time,
                    frames: recordedData.length,
                    parameters: {
                        G: gameState.G,
                        dt: gameState.dt,
                        centralMass: gameState.centralMassValue,
                        enableCentralMass: gameState.enableCentralMass
                    }
                },
                data: recordedData
            };

            const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `three_body_trajectory_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        function loadTrajectory(event) {
            const file = event.target.files[0];
            if (!file) return;

            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    playbackData = data.data;
                    playbackIndex = 0;

                    // 恢复参数
                    if (data.metadata && data.metadata.parameters) {
                        const params = data.metadata.parameters;
                        gameState.G = params.G || 1.0;
                        gameState.dt = params.dt || 0.01;
                        gameState.centralMassValue = params.centralMass || 1000;
                        gameState.enableCentralMass = params.enableCentralMass || false;

                        // 更新UI
                        document.getElementById('gravitySlider').value = gameState.G;
                        document.getElementById('timeStepSlider').value = gameState.dt;
                        document.getElementById('centralMassSlider').value = gameState.centralMassValue;
                        document.getElementById('enableCentralMass').checked = gameState.enableCentralMass;

                        updateGravity(gameState.G);
                        updateTimeStep(gameState.dt);
                        updateCentralMass(gameState.centralMassValue);
                        toggleCentralMass();
                    }

                    // 设置回放滑块
                    document.getElementById('playbackSlider').max = playbackData.length - 1;

                    console.log(`加载轨迹数据成功，共 ${playbackData.length} 帧`);
                    alert(`轨迹数据加载成功！\n时长: ${data.metadata.duration.toFixed(2)}秒\n帧数: ${data.metadata.frames}`);
                } catch (error) {
                    console.error('加载轨迹数据失败:', error);
                    alert('文件格式错误，无法加载轨迹数据');
                }
            };
            reader.readAsText(file);
        }

        function exportData() {
            const physics = calculatePhysics();
            const exportData = {
                timestamp: new Date().toISOString(),
                currentState: {
                    time: gameState.time,
                    bodies: bodies.map(body => ({
                        name: body.name,
                        mass: body.mass,
                        position: body.pos,
                        velocity: body.vel,
                        speed: Math.sqrt(body.vel[0]*body.vel[0] + body.vel[1]*body.vel[1] + body.vel[2]*body.vel[2])
                    })),
                    physics: physics,
                    parameters: {
                        G: gameState.G,
                        dt: gameState.dt,
                        centralMass: gameState.centralMassValue,
                        enableCentralMass: gameState.enableCentralMass
                    }
                }
            };

            const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `three_body_state_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
            a.click();
            URL.revokeObjectURL(url);
        }

        // 回放控制
        function togglePlayback() {
            if (playbackData.length === 0) {
                alert('请先加载轨迹数据');
                return;
            }

            gameState.playback = !gameState.playback;
            gameState.running = false;

            document.getElementById('playbackBtn').textContent = gameState.playback ? '⏸️' : '▶️';
            document.getElementById('startBtn').textContent = '开始模拟';

            if (gameState.playback) {
                playbackLoop();
            }
        }

        function playbackLoop() {
            if (!gameState.playback || playbackIndex >= playbackData.length) {
                gameState.playback = false;
                document.getElementById('playbackBtn').textContent = '▶️';
                return;
            }

            const frame = playbackData[playbackIndex];

            // 更新天体位置
            bodies.forEach((body, index) => {
                if (frame.bodies[index]) {
                    body.pos = [...frame.bodies[index].pos];
                    body.vel = [...frame.bodies[index].vel];
                    body.mass = frame.bodies[index].mass;
                    body.mesh.position.set(body.pos[0], body.pos[1], body.pos[2]);
                }
            });

            gameState.time = frame.time;

            // 更新回放滑块
            document.getElementById('playbackSlider').value = playbackIndex;
            document.getElementById('playbackTime').textContent = frame.time.toFixed(2) + 's';

            playbackIndex++;

            setTimeout(() => {
                if (gameState.playback) {
                    playbackLoop();
                }
            }, 50); // 20 FPS回放
        }

        function playbackStart() {
            if (playbackData.length > 0) {
                playbackIndex = 0;
                document.getElementById('playbackSlider').value = 0;
            }
        }

        function playbackEnd() {
            if (playbackData.length > 0) {
                playbackIndex = playbackData.length - 1;
                document.getElementById('playbackSlider').value = playbackIndex;
            }
        }

        function playbackPrev() {
            if (playbackIndex > 0) {
                playbackIndex = Math.max(0, playbackIndex - 10);
                document.getElementById('playbackSlider').value = playbackIndex;
            }
        }

        function playbackNext() {
            if (playbackIndex < playbackData.length - 1) {
                playbackIndex = Math.min(playbackData.length - 1, playbackIndex + 10);
                document.getElementById('playbackSlider').value = playbackIndex;
            }
        }

        // 视图控制
        function zoomIn() {
            const distance = camera.position.distanceTo(controls.target);
            const direction = new THREE.Vector3().subVectors(camera.position, controls.target).normalize();
            camera.position.copy(controls.target).add(direction.multiplyScalar(distance * 0.8));
        }

        function zoomOut() {
            const distance = camera.position.distanceTo(controls.target);
            const direction = new THREE.Vector3().subVectors(camera.position, controls.target).normalize();
            camera.position.copy(controls.target).add(direction.multiplyScalar(distance * 1.25));
        }

        function resetView() {
            camera.position.set(50, 50, 50);
            controls.target.set(0, 0, 0);
            controls.update();
        }

        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }

        // 回放滑块事件
        document.addEventListener('DOMContentLoaded', function() {
            const playbackSlider = document.getElementById('playbackSlider');
            playbackSlider.addEventListener('input', function() {
                if (playbackData.length > 0) {
                    playbackIndex = parseInt(this.value);
                    const frame = playbackData[playbackIndex];

                    if (frame) {
                        bodies.forEach((body, index) => {
                            if (frame.bodies[index]) {
                                body.pos = [...frame.bodies[index].pos];
                                body.vel = [...frame.bodies[index].vel];
                                body.mass = frame.bodies[index].mass;
                                body.mesh.position.set(body.pos[0], body.pos[1], body.pos[2]);
                            }
                        });

                        gameState.time = frame.time;
                        document.getElementById('playbackTime').textContent = frame.time.toFixed(2) + 's';
                    }
                }
            });
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initScene();
            animate(0);

            console.log('🌌 高级3D三体运动模拟器已启动！');
            console.log('功能特色:');
            console.log('• 3D可视化和交互控制');
            console.log('• 中心质心引力约束');
            console.log('• 完整物理参数显示');
            console.log('• 轨迹录制和回放');
            console.log('• 多种预设配置');
            console.log('享受探索宇宙奥秘的乐趣吧！');
        });
    </script>
</body>
</html>